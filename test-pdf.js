// Simple test to verify PDF generation is working
import { generateReturnLabelPDF } from './packages/pdf-generator/dist/index.js';

async function testPDF() {
  try {
    console.log('Testing PDF generation...');

    const testData = {
      orderName: '#12345',
      returnNumber: 'RET-12345',
      customerName: 'Test Customer',
      customerAddress: '123 Test St, Test City',
      companyName: 'Casefinite JP',
      companyAddress: 'Casefinite Address',
      returnDepartment: 'Returns Department',
    };

    const result = await generateReturnLabelPDF(testData);

    console.log('PDF generation successful!');
    console.log('Filename:', result.filename);
    console.log('Content type:', result.type);
    console.log('Content length:', result.content.length);
  } catch (error) {
    console.error('PDF generation failed:', error);
  }
}

testPDF();
