// #!/usr/bin/env node

/**
 * Simple test script to verify the email queue system
 * Run with: node test-queue.js
 */

const API_BASE = 'http://localhost:3002';

async function testEmailQueue() {
  // console.log('🧪 Testing Email Queue System...\n');
  // try {
  //   // Test 1: Check queue stats
  //   console.log('1. Checking queue statistics...');
  //   const statsResponse = await fetch(`${API_BASE}/api/email-queue/stats`);
  //   const stats = await statsResponse.json();
  //   if (stats.success) {
  //     console.log('✅ Queue stats retrieved successfully');
  //     console.log(`   - Waiting: ${stats.data.waiting}`);
  //     console.log(`   - Active: ${stats.data.active}`);
  //     console.log(`   - Completed: ${stats.data.completed}`);
  //     console.log(`   - Failed: ${stats.data.failed}`);
  //     console.log(`   - Redis Healthy: ${stats.data.redisHealthy}`);
  //   } else {
  //     console.log('❌ Failed to get queue stats:', stats.error);
  //   }
  //   console.log('\n2. Adding test email to queue...');
  //   // Test 2: Add a test email to queue
  //   const testEmail = {
  //     type: 'return-request-auto-approved',
  //     to: '<EMAIL>',
  //     returnNumber: `TEST-${Date.now()}`,
  //     refundDays: '5-7',
  //     priority: 8,
  //     returnLabelOption: 'print',
  //     orderName: 'Test Order',
  //     customerName: 'John Doe',
  //     customerAddress: '123 Test St, Anytown, USA',
  //     companyName: 'Test Company',
  //     companyAddress: '456 Test Ave, Anytown, USA',
  //     returnDepartment: 'Returns Dept',
  //   };
  //   const addResponse = await fetch(`${API_BASE}/api/email-queue/add`, {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json',
  //     },
  //     body: JSON.stringify(testEmail),
  //   });
  //   const addResult = await addResponse.json();
  //   if (addResult.success) {
  //     console.log('✅ Test email added to queue successfully');
  //     console.log(`   - Job ID: ${addResult.jobId}`);
  //     console.log(`   - Email Type: ${testEmail.type}`);
  //     console.log(`   - Return Number: ${testEmail.returnNumber}`);
  //   } else {
  //     console.log('❌ Failed to add email to queue:', addResult.error);
  //   }
  //   console.log('\n3. Checking updated queue statistics...');
  //   // Test 3: Check stats again to see the change
  //   const updatedStatsResponse = await fetch(
  //     `${API_BASE}/api/email-queue/stats`
  //   );
  //   const updatedStats = await updatedStatsResponse.json();
  //   if (updatedStats.success) {
  //     console.log('✅ Updated queue stats retrieved');
  //     console.log(`   - Waiting: ${updatedStats.data.waiting}`);
  //     console.log(`   - Active: ${updatedStats.data.active}`);
  //     console.log(`   - Total: ${updatedStats.data.total}`);
  //   }
  //   console.log('\n🎉 Email queue system test completed!');
  //   console.log('\nNext steps:');
  //   console.log('1. Start the email worker: cd apps/email-worker && pnpm dev');
  //   console.log(
  //     '2. Monitor the admin interface: http://localhost:3001/email-queue'
  //   );
  //   console.log('3. Check worker logs for email processing');
  // } catch (error) {
  //   console.error('❌ Test failed:', error.message);
  //   console.log('\nTroubleshooting:');
  //   console.log(
  //     '1. Make sure the API server is running: cd apps/api && pnpm dev'
  //   );
  //   console.log('2. Check Redis connection in environment variables');
  //   console.log('3. Verify SendGrid configuration');
  // }
}

// Run the test
testEmailQueue();
